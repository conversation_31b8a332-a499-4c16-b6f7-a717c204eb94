"""
End-to-End Test Scenarios for vinagent project.

This module tests complete workflows and user journeys:
- Agent creation and basic conversation flows
- Tool integration and execution workflows
- Memory persistence across sessions
- Error handling in real-world scenarios
- Integration between vinagent and GAAPF components
"""

import pytest
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Import components for testing
from langchain_core.messages import HumanMessage, AIMessage
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_together import ChatTogether

# Import vinagent components
from vinagent.agent.agent import Agent
from vinagent.memory.memory import Memory
from vinagent.register.tool import ToolManager

# Import GAAPF components (with error handling)
try:
    from gaapf.config.env_config import EnvironmentConfig
    from gaapf.core.knowledge_graph import KnowledgeGraphManager
    from gaapf.core.memory_system import MemorySystemManager
    GAAPF_AVAILABLE = True
except ImportError:
    GAAPF_AVAILABLE = False


class TestBasicAgentWorkflow:
    """Test basic agent creation and conversation workflows."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM that behaves like a real LLM."""
        llm = Mock()
        llm.model_name = "mock-gpt-4"
        
        def mock_invoke(messages):
            # Extract the last message content
            if isinstance(messages, list) and len(messages) > 0:
                last_message = messages[-1]
                if hasattr(last_message, 'content'):
                    user_input = last_message.content
                else:
                    user_input = str(last_message)
            else:
                user_input = str(messages)
            
            # Generate contextual responses
            if "hello" in user_input.lower():
                response_content = "Hello! I'm a helpful AI assistant. How can I help you today?"
            elif "weather" in user_input.lower():
                response_content = "I'd be happy to help with weather information, but I don't have access to current weather data. You might want to check a weather service."
            elif "calculate" in user_input.lower() or "math" in user_input.lower():
                response_content = "I can help with calculations. What would you like me to calculate?"
            elif "goodbye" in user_input.lower() or "bye" in user_input.lower():
                response_content = "Goodbye! It was nice chatting with you."
            else:
                response_content = f"I understand you said: '{user_input}'. How can I assist you further?"
            
            # Return mock response object
            mock_response = Mock()
            mock_response.content = response_content
            return mock_response
        
        llm.invoke = mock_invoke
        return llm
    
    @pytest.fixture
    def basic_tools_config(self, temp_data_path):
        """Create basic tools configuration."""
        tools_file = temp_data_path / "tools.json"
        tools_data = {
            "calculator": {
                "name": "calculator",
                "description": "Perform basic mathematical calculations",
                "tool_type": "function"
            },
            "weather": {
                "name": "weather",
                "description": "Get weather information",
                "tool_type": "function"
            }
        }
        tools_file.write_text(json.dumps(tools_data, indent=2))
        return tools_file
    
    def test_agent_creation_and_basic_conversation(self, mock_llm, basic_tools_config):
        """Test creating an agent and having a basic conversation."""
        # Create agent
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=basic_tools_config,
            description="A helpful AI assistant for testing",
            skills=["conversation", "general assistance"]
        )
        
        # Test basic conversation flow
        responses = []
        
        # Greeting
        response1 = agent.invoke("Hello there!")
        responses.append(response1.content)
        assert "hello" in response1.content.lower()
        
        # Question about capabilities
        response2 = agent.invoke("What can you help me with?")
        responses.append(response2.content)
        assert response2.content is not None
        
        # Farewell
        response3 = agent.invoke("Goodbye!")
        responses.append(response3.content)
        assert "goodbye" in response3.content.lower()
        
        # Verify all responses were generated
        assert len(responses) == 3
        assert all(response for response in responses)
    
    def test_agent_with_memory_persistence(self, mock_llm, basic_tools_config, temp_data_path):
        """Test agent memory persistence across conversations."""
        memory_path = temp_data_path / "conversation_memory.jsonl"
        
        # Create agent with memory
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=basic_tools_config,
            memory_path=memory_path,
            description="Agent with memory for testing"
        )
        
        user_id = "test_user_123"
        
        # First conversation
        response1 = agent.invoke("My name is Alice", user_id=user_id, is_save_memory=False)
        assert response1 is not None
        
        # Manually save to memory (since automatic save might have issues with mock LLM)
        if agent.memory:
            conversation_data = [
                {"role": "user", "content": "My name is Alice"},
                {"role": "assistant", "content": response1.content}
            ]
            agent.memory.save_memory(conversation_data, memory_path, user_id)
        
        # Verify memory file was created
        assert memory_path.exists()
        
        # Create new agent instance (simulating new session)
        agent2 = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=basic_tools_config,
            memory_path=memory_path,
            description="Second agent instance"
        )
        
        # Load memory and verify persistence
        if agent2.memory:
            loaded_memory = agent2.memory.load_memory(user_id=user_id, load_type='list')
            assert loaded_memory is not None
            if loaded_memory:  # If memory was successfully loaded
                assert any("Alice" in str(item) for item in loaded_memory)
    
    @pytest.mark.asyncio
    async def test_async_agent_workflow(self, mock_llm, basic_tools_config):
        """Test asynchronous agent workflow."""
        # Add async support to mock LLM
        async def mock_ainvoke(messages):
            return mock_llm.invoke(messages)
        
        mock_llm.ainvoke = mock_ainvoke
        
        # Create agent
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=basic_tools_config,
            description="Async agent for testing"
        )
        
        # Test async conversation
        response = await agent.ainvoke("Hello, this is an async test!")
        
        assert response is not None
        assert hasattr(response, 'content')
        assert response.content is not None


class TestToolIntegrationWorkflow:
    """Test tool integration and execution workflows."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_llm_with_tools(self):
        """Mock LLM that can suggest tool usage."""
        llm = Mock()
        llm.model_name = "mock-gpt-4-tools"
        
        def mock_invoke(messages):
            # Extract user input
            if isinstance(messages, list) and len(messages) > 0:
                last_message = messages[-1]
                user_input = getattr(last_message, 'content', str(last_message))
            else:
                user_input = str(messages)
            
            # Generate tool-aware responses
            if "calculate" in user_input.lower() or "math" in user_input.lower():
                response_content = "I can help with calculations. For complex math, I would use a calculator tool."
            elif "weather" in user_input.lower():
                response_content = "To get current weather information, I would need to use a weather tool."
            else:
                response_content = f"I understand: '{user_input}'. I have access to various tools to help you."
            
            mock_response = Mock()
            mock_response.content = response_content
            return mock_response
        
        llm.invoke = mock_invoke
        return llm
    
    @pytest.fixture
    def tools_with_implementations(self, temp_data_path):
        """Create tools configuration with mock implementations."""
        tools_file = temp_data_path / "tools.json"
        tools_data = {
            "simple_calculator": {
                "name": "simple_calculator",
                "description": "Perform simple arithmetic operations",
                "tool_type": "function",
                "parameters": {
                    "operation": "string",
                    "a": "number",
                    "b": "number"
                }
            }
        }
        tools_file.write_text(json.dumps(tools_data, indent=2))
        return tools_file
    
    def test_tool_discovery_and_loading(self, mock_llm_with_tools, tools_with_implementations):
        """Test tool discovery and loading process."""
        # Create agent with tools
        agent = Agent(
            llm=mock_llm_with_tools,
            tools=[],
            tools_path=tools_with_implementations,
            description="Agent with tool capabilities"
        )
        
        # Verify tools manager was created
        assert agent.tools_manager is not None
        assert isinstance(agent.tools_manager, ToolManager)
        
        # Verify tools configuration was loaded
        assert agent.tools_path == tools_with_implementations
    
    def test_tool_related_conversation(self, mock_llm_with_tools, tools_with_implementations):
        """Test conversation that involves tool-related topics."""
        agent = Agent(
            llm=mock_llm_with_tools,
            tools=[],
            tools_path=tools_with_implementations,
            description="Tool-aware agent"
        )
        
        # Ask about mathematical calculation
        response = agent.invoke("Can you help me calculate 15 + 27?")
        
        assert response is not None
        assert "calculate" in response.content.lower() or "calculator" in response.content.lower()
        
        # Ask about weather
        response2 = agent.invoke("What's the weather like today?")
        
        assert response2 is not None
        assert "weather" in response2.content.lower()


class TestErrorHandlingScenarios:
    """Test error handling in real-world scenarios."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def failing_llm(self):
        """Mock LLM that fails intermittently."""
        llm = Mock()
        llm.model_name = "failing-llm"
        llm.call_count = 0
        
        def mock_invoke(messages):
            llm.call_count += 1
            if llm.call_count % 3 == 0:  # Fail every third call
                raise Exception("LLM service temporarily unavailable")
            
            mock_response = Mock()
            mock_response.content = f"Response #{llm.call_count}: I'm working normally."
            return mock_response
        
        llm.invoke = mock_invoke
        return llm
    
    def test_agent_resilience_to_llm_failures(self, failing_llm, temp_data_path):
        """Test agent behavior when LLM fails."""
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        
        agent = Agent(
            llm=failing_llm,
            tools=[],
            tools_path=tools_file,
            description="Resilient agent"
        )
        
        # First call should succeed
        response1 = agent.invoke("Hello")
        assert response1 is not None
        assert "Response #1" in response1.content
        
        # Second call should succeed
        response2 = agent.invoke("How are you?")
        assert response2 is not None
        assert "Response #2" in response2.content
        
        # Third call should fail
        with pytest.raises(Exception, match="LLM service temporarily unavailable"):
            agent.invoke("This should fail")
    
    def test_agent_with_corrupted_memory_file(self, temp_data_path):
        """Test agent behavior with corrupted memory file."""
        # Create corrupted memory file
        memory_path = temp_data_path / "corrupted_memory.jsonl"
        memory_path.write_text("invalid json content that cannot be parsed")
        
        mock_llm = Mock()
        mock_llm.model_name = "test-llm"
        mock_llm.invoke.return_value = Mock(content="I'm working despite memory issues")
        
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        
        # Agent should handle corrupted memory gracefully
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            memory_path=str(memory_path),  # Use string path to avoid Path issues
            description="Agent with corrupted memory"
        )
        
        # Should still be able to have conversations
        response = agent.invoke("Hello despite memory issues")
        assert response is not None
        assert "working" in response.content
    
    def test_agent_with_missing_tools_file(self, temp_data_path):
        """Test agent behavior when tools file is missing."""
        mock_llm = Mock()
        mock_llm.model_name = "test-llm"
        mock_llm.invoke.return_value = Mock(content="Working without tools")
        
        missing_tools_path = temp_data_path / "nonexistent_tools.json"
        
        # Agent should handle missing tools file gracefully
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=missing_tools_path,
            description="Agent with missing tools file"
        )
        
        # Should still work for basic conversations
        response = agent.invoke("Hello")
        assert response is not None
        assert response.content is not None


class TestGAAPFIntegration:
    """Test integration between vinagent and GAAPF components."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.mark.skipif(not GAAPF_AVAILABLE, reason="GAAPF components not available")
    def test_environment_config_integration(self):
        """Test GAAPF environment configuration."""
        # Test basic environment config creation
        config = EnvironmentConfig()

        assert config is not None
        assert hasattr(config, 'get_config')

        # Test configuration retrieval
        config_data = config.get_config()
        assert isinstance(config_data, dict)
    
    @pytest.mark.skipif(not GAAPF_AVAILABLE, reason="GAAPF components not available")
    def test_knowledge_graph_basic_operations(self, temp_data_path):
        """Test basic knowledge graph operations."""
        try:
            # Initialize knowledge graph manager
            kg = KnowledgeGraphManager(data_path=temp_data_path)

            assert kg is not None

            # Test basic operations if available
            if hasattr(kg, 'add_concept'):
                from gaapf.core.knowledge_graph import ConceptNode
                test_concept = ConceptNode(
                    concept_id="test_concept",
                    name="Test Concept",
                    framework="test_framework"
                )
                kg.add_concept(test_concept)

        except Exception as e:
            # If KnowledgeGraphManager has different interface, skip gracefully
            pytest.skip(f"KnowledgeGraphManager interface different than expected: {e}")
    
    @pytest.mark.skipif(not GAAPF_AVAILABLE, reason="GAAPF components not available")
    def test_memory_system_integration(self, temp_data_path):
        """Test GAAPF memory system integration."""
        memory_path = temp_data_path / "gaapf_memory.json"

        try:
            # Initialize memory system manager
            memory_system = MemorySystemManager(data_path=temp_data_path)

            assert memory_system is not None

            # Test basic memory operations if available
            if hasattr(memory_system, 'store_memory'):
                memory_system.store_memory("test_user", "test_memory", {"content": "test"})

        except Exception as e:
            # If MemorySystemManager has different interface, skip gracefully
            pytest.skip(f"MemorySystemManager interface different than expected: {e}")


class TestPerformanceScenarios:
    """Test performance-related scenarios."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_multiple_concurrent_conversations(self, temp_data_path):
        """Test handling multiple concurrent conversations."""
        mock_llm = Mock()
        mock_llm.model_name = "concurrent-test-llm"
        
        def mock_invoke(messages):
            # Simulate processing time
            import time
            time.sleep(0.01)  # 10ms delay
            
            mock_response = Mock()
            mock_response.content = f"Response to: {messages}"
            return mock_response
        
        mock_llm.invoke = mock_invoke
        
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        
        # Create agent
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            description="Concurrent conversation agent"
        )
        
        # Simulate multiple users
        user_ids = ["user1", "user2", "user3"]
        responses = []
        
        for user_id in user_ids:
            response = agent.invoke(f"Hello from {user_id}", user_id=user_id)
            responses.append(response)
        
        # Verify all conversations were handled
        assert len(responses) == 3
        assert all(response is not None for response in responses)
    
    def test_large_memory_handling(self, temp_data_path):
        """Test handling of large memory files."""
        memory_path = temp_data_path / "large_memory.jsonl"
        
        # Create large memory file
        large_memory_data = {}
        for i in range(100):  # 100 users with conversation history
            user_id = f"user_{i}"
            large_memory_data[user_id] = [
                {"role": "user", "content": f"Message {j} from {user_id}"}
                for j in range(50)  # 50 messages per user
            ]
        
        memory_path.write_text(json.dumps(large_memory_data, indent=2))
        
        # Test memory loading performance
        memory = Memory(memory_path=memory_path)
        
        # Load memory for a specific user
        user_memory = memory.load_memory(user_id="user_50", load_type='list')
        
        assert user_memory is not None
        if user_memory:  # If memory was loaded successfully
            assert len(user_memory) == 50
